package com.wonderslate.data

import com.wonderslate.usermanagement.UserManagementService
import grails.transaction.Transactional

@Transactional
class LiveMockTestsService {
    UserManagementService userManagementService
    /**
     * Common method to get quiz details from ResourceDtl using mcqResId
     */
    private def getQuizDetails(Long mcqResId, def session, def request, def response) {
        try {
            ResourceDtl resourceDtl = ResourceDtl.findById(mcqResId)
            if (!resourceDtl) {
                return null
            }

            // Count MCQs using resLink (quiz_id in ObjectiveMst)
            Integer mcqCount = 0
            if (resourceDtl.resLink) {
                mcqCount = ObjectiveMst.countByQuizId(Integer.parseInt(resourceDtl.resLink))
            }

            boolean hasTestAccess = userManagementService.canSeeResourceCheck(resourceDtl, session, request, response)

            return [
                resourceDtl: resourceDtl,
                mcqCount: mcqCount,
                hasTestAccess: hasTestAccess
            ]
        } catch (Exception e) {
            log.error("Error getting quiz details for mcqResId ${mcqResId}: ${e.message}", e)
            return null
        }
    }

    /**
     * Get all ongoing mock tests with pagination
     * A test is ongoing if current date is between test start date and test end date
     */
    def getOngoingMockTests(Map paginationParams = [:], def session, def request, def response) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // Get total count first
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testStartDate IS NOT NULL
                AND rd.testEndDate IS NOT NULL
                AND rd.testStartDate <= :currentDate
                AND rd.testEndDate > :currentDate
            """, [currentDate: currentDate])[0]

            // Get paginated results
            def ongoingMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testStartDate IS NOT NULL
                AND rd.testEndDate IS NOT NULL
                AND rd.testStartDate <= :currentDate
                AND rd.testEndDate > :currentDate
                ORDER BY rd.testStartDate DESC
            """, [currentDate: currentDate], [max: max, offset: offset])

            def result = []
            ongoingMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl
                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: 'ongoing'
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting ongoing mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch ongoing mock tests']
        }
    }

    /**
     * Get all upcoming mock tests with pagination
     * A test is upcoming if current date is less than test start date
     */
    def getUpcomingMockTests(Map paginationParams = [:], def session, def request, def response) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // Get total count first
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testStartDate IS NOT NULL
                AND rd.testStartDate > :currentDate
            """, [currentDate: currentDate])[0]

            // Get paginated results
            def upcomingMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testStartDate IS NOT NULL
                AND rd.testStartDate > :currentDate
                ORDER BY rd.testStartDate ASC
            """, [currentDate: currentDate], [max: max, offset: offset])

            def result = []
            upcomingMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl
                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: 'upcoming'
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting upcoming mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch upcoming mock tests']
        }
    }

    /**
     * Get all completed mock tests with pagination
     * A test is completed if test end date is less than current date
     */
    def getCompletedMockTests(Map paginationParams = [:]) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // Get total count first
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testEndDate IS NOT NULL
                AND rd.testEndDate < :currentDate
            """, [currentDate: currentDate])[0]

            // Get paginated results
            def completedMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                AND rd.testEndDate IS NOT NULL
                AND rd.testEndDate < :currentDate
                ORDER BY rd.testEndDate DESC
            """, [currentDate: currentDate], [max: max, offset: offset])

            def result = []
            completedMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl
                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: 'completed'
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting completed mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch completed mock tests']
        }
    }

    /**
     * Get all mock tests (active, completed, and upcoming) with pagination
     */
    def getAllMockTests(Map paginationParams = [:]) {
        try {
            Date currentDate = new Date()

            // Pagination parameters
            int max = paginationParams.max ?: 10
            int offset = paginationParams.offset ?: 0

            // Get total count first
            def totalCount = LiveMockMst.executeQuery("""
                SELECT COUNT(lm) FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
            """)[0]

            // Get paginated results
            def allMockTests = LiveMockMst.executeQuery("""
                SELECT lm FROM LiveMockMst lm, ResourceDtl rd
                WHERE lm.mcqResId = rd.id
                AND (lm.isDeleted IS NULL OR lm.isDeleted = 'false')
                ORDER BY rd.testStartDate DESC
            """, [:], [max: max, offset: offset])

            def result = []
            allMockTests.each { liveMockMst ->
                def quizDetails = getQuizDetails(liveMockMst.mcqResId, session, request, response)
                if (quizDetails) {
                    def resourceDtl = quizDetails.resourceDtl

                    // Determine status
                    String status = 'upcoming'
                    if (resourceDtl.testStartDate && resourceDtl.testEndDate) {
                        if (resourceDtl.testEndDate < currentDate) {
                            status = 'completed'
                        } else if (resourceDtl.testStartDate <= currentDate && resourceDtl.testEndDate > currentDate) {
                            status = 'active'
                        }
                    }

                    result.add([
                        id: liveMockMst.id,
                        mcqResId: liveMockMst.mcqResId,
                        createdBy: liveMockMst.createdBy,
                        dateCreated: liveMockMst.dateCreated,
                        resourceName: resourceDtl.resourceName,
                        testStartDate: resourceDtl.testStartDate,
                        testEndDate: resourceDtl.testEndDate,
                        mcqCount: quizDetails.mcqCount,
                        totalTime: resourceDtl.mcqTotalTime,
                        language1: resourceDtl.language1,
                        language2: resourceDtl.language2,
                        hasTestAccess: quizDetails.hasTestAccess,
                        status: status
                    ])
                }
            }

            return [
                status: 'success',
                data: result,
                pagination: [
                    total: totalCount,
                    max: max,
                    offset: offset,
                    currentPage: Math.floor(offset / max) + 1,
                    totalPages: Math.ceil(totalCount / max)
                ]
            ]
        } catch (Exception e) {
            log.error("Error getting all mock tests: ${e.message}", e)
            return [status: 'error', message: 'Failed to fetch all mock tests']
        }
    }

    /**
     * Delete a mock test by setting isDeleted to true
     */
    def deleteMockTest(Long mockTestId, String deletedBy) {
        try {
            LiveMockMst liveMockMst = LiveMockMst.get(mockTestId)

            if (!liveMockMst) {
                return [status: 'error', message: 'Mock test not found']
            }

            if (liveMockMst.isDeleted == 'true') {
                return [status: 'error', message: 'Mock test is already deleted']
            }

            liveMockMst.isDeleted = 'true'
            liveMockMst.deletedBy = deletedBy
            liveMockMst.save(failOnError: true, flush: true)

            return [status: 'success', message: 'Mock test deleted successfully']
        } catch (Exception e) {
            log.error("Error deleting mock test: ${e.message}", e)
            return [status: 'error', message: 'Failed to delete mock test']
        }
    }
}
