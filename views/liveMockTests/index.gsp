<g:render template="/${session['entryController']}/navheader_new"></g:render>

<style>
    body {
        background: #f8f9fa !important;
    }
    main {
        min-height: 75vh;
        margin-top: 4rem;
    }
    .ws_container {
        width: calc(100% - 30%);
        margin: 0 auto;
    }
    .liveMockTests {
        margin-top: 2rem;
        margin-bottom: 3rem;
    }
    .liveMockTests__title {
        font-size: 1.7rem;
        margin-bottom: 2rem;
        text-align: center;
        color: #333;
    }

    /* Modern Tab Navigation */
    .nav-tabs {
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 30px;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0;
    }

    .nav-tabs li {
        margin-bottom: -2px;
        border: none;
    }

    .nav-tabs li a {
        border: none;
        border-radius: 8px 8px 0 0;
        padding: 12px 24px;
        color: #6c757d;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        background: #f8f9fa;
        margin-right: 4px;
        position: relative;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .nav-tabs li a:hover {
        background: #e9ecef;
        color: #495057;
        border: none;
    }

    .nav-tabs li.active a {
        background: #fff;
        color: #007bff;
        border: none;
        border-bottom: 2px solid #007bff;
        font-weight: 600;
    }

    .nav-tabs li a i {
        font-size: 16px;
    }

    /* Tab Content */
    .tab-content {
        background: #fff;
        border-radius: 8px;
        padding: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .tab-pane {
        padding: 30px;
        min-height: 400px;
    }

    /* Test Cards Grid Layout */
    .tests-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .test-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .test-card:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    /* Status Badge */
    .status-badge {
        position: absolute;
        top: 12px;
        left: 12px;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
        color: white;
    }

    .status-badge.live-test {
        background: #dc3545;
    }

    .status-badge.free {
        background: #28a745;
        margin-left: 70px;
    }

    /* Card Content */
    .test-card-content {
        padding: 50px 20px 20px;
    }

    .test-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.4;
        min-height: 44px;
    }

    /* Test Info */
    .test-info {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #6c757d;
        font-size: 14px;
    }

    .info-item i {
        width: 16px;
        color: #6c757d;
    }

    /* Date Time */
    .test-datetime {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #6c757d;
        font-size: 13px;
        margin-bottom: 15px;
    }

    /* Language Flags */
    .language-flags {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;
    }

    .language-flag {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 4px 8px;
        background: #e9ecef;
        border-radius: 4px;
        font-size: 12px;
        color: #495057;
    }

    .flag-icon {
        width: 16px;
        height: 12px;
        background: #007bff;
        border-radius: 2px;
        display: inline-block;
    }

    /* Action Button */
    .test-action {
        width: 100%;
    }

    .action-btn {
        width: 100%;
        padding: 10px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-start-now {
        background: #17a2b8;
        color: white;
    }

    .btn-start-now:hover {
        background: #138496;
        color: white;
    }

    .btn-register {
        background: #28a745;
        color: white;
    }

    .btn-register:hover {
        background: #218838;
        color: white;
    }

    .btn-view-results {
        background: #6c757d;
        color: white;
    }

    .btn-view-results:hover {
        background: #5a6268;
        color: white;
    }

    .btn-disabled {
        background: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
    }

    /* Loading and Empty States */
    .loading-spinner {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-tests {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-tests h4 {
        margin-bottom: 10px;
        color: #495057;
    }

    /* Pagination */
    .pagination {
        justify-content: center;
        margin-top: 30px;
    }

    .pagination li a,
    .pagination li span {
        padding: 8px 12px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
        color: #007bff;
        text-decoration: none;
        border-radius: 4px;
    }

    .pagination li.active span {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    .pagination li.disabled span {
        color: #6c757d;
        background: #fff;
        border-color: #dee2e6;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .ws_container {
            width: calc(100% - 4%);
        }
        .tests-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        .test-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }
    }

    @media (max-width: 480px) {
        .test-card-content {
            padding: 45px 15px 15px;
        }
        .status-badge.free {
            margin-left: 60px;
        }
    }
</style>

<main>
    <div class="ws_container">
        <section class="liveMockTests">
            <h1 class="liveMockTests__title">Live Mock Tests</h1>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#ongoing-tests" aria-controls="ongoing-tests" role="tab" onclick="switchTab(event, 'ongoing')" data-tab="ongoing">
                        <i class="fa fa-play-circle"></i> On-Going Tests
                    </a>
                </li>
                <li role="presentation">
                    <a href="#upcoming-tests" aria-controls="upcoming-tests" role="tab" onclick="switchTab(event, 'upcoming')" data-tab="upcoming">
                        <i class="fa fa-clock-o"></i> Up-coming
                    </a>
                </li>
                <li role="presentation">
                    <a href="#completed-tests" aria-controls="completed-tests" role="tab" onclick="switchTab(event, 'completed')" data-tab="completed">
                        <i class="fa fa-check-circle"></i> Completed
                    </a>
                </li>
                <li role="presentation">
                    <a href="#all-tests" aria-controls="all-tests" role="tab" onclick="switchTab(event, 'all')" data-tab="all">
                        <i class="fa fa-list"></i> All
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Ongoing Tests Tab -->
                <div role="tabpanel" class="tab-pane in active" id="ongoing-tests">
                    <div id="ongoing-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading ongoing tests...</p>
                        </div>
                    </div>
                    <div id="ongoing-tests-pagination"></div>
                </div>

                <!-- Upcoming Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="upcoming-tests">
                    <div id="upcoming-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading upcoming tests...</p>
                        </div>
                    </div>
                    <div id="upcoming-tests-pagination"></div>
                </div>

                <!-- Completed Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="completed-tests">
                    <div id="completed-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading completed tests...</p>
                        </div>
                    </div>
                    <div id="completed-tests-pagination"></div>
                </div>

                <!-- All Tests Tab -->
                <div role="tabpanel" class="tab-pane fade" id="all-tests">
                    <div id="all-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading all tests...</p>
                        </div>
                    </div>
                    <div id="all-tests-pagination"></div>
                </div>
            </div>
        </section>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var currentTab = 'ongoing';
    var currentPage = {
        ongoing: 1,
        upcoming: 1,
        completed: 1,
        all: 1
    };
    var pageSize = 10;

    // Initialize the page
    loadTests('ongoing', 1);

    // Function to switch tabs
    window.switchTab = function(event, tabType) {
        event.preventDefault();

        // Update tab navigation
        var tabs = document.querySelectorAll('.nav-tabs li');
        var tabPanes = document.querySelectorAll('.tab-pane');

        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });

        tabPanes.forEach(function(pane) {
            pane.classList.remove('active', 'in');
        });

        // Activate current tab
        event.target.closest('li').classList.add('active');
        document.getElementById(tabType + '-tests').classList.add('active', 'in');

        currentTab = tabType;
        loadTests(tabType, currentPage[tabType]);
    };

    // Function to load tests based on tab type and page
    function loadTests(tabType, page) {
        if (!page) page = 1;

        var contentId = tabType + '-tests-content';
        var paginationId = tabType + '-tests-pagination';

        // Show loading spinner
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="loading-spinner">' +
            '<i class="fa fa-spinner fa-spin fa-2x"></i>' +
            '<p>Loading ' + tabType + ' tests...</p>' +
            '</div>';

        // Clear pagination
        document.getElementById(paginationId).innerHTML = '';

        var apiUrl;
        switch(tabType) {
            case 'ongoing':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getOngoingMockTests')}';
                break;
            case 'upcoming':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getUpcomingMockTests')}';
                break;
            case 'completed':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getCompletedMockTests')}';
                break;
            case 'all':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getAllMockTests')}';
                break;
        }

        // Create XMLHttpRequest
        var xhr = new XMLHttpRequest();
        xhr.open('GET', apiUrl + '?max=' + pageSize + '&page=' + page, true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.status === 'success') {
                            renderTests(response.data, contentId);
                            renderPagination(response.pagination, paginationId, tabType);
                            currentPage[tabType] = page;
                        } else {
                            showError(contentId, response.message || 'Failed to load tests');
                        }
                    } catch (e) {
                        console.error('Error parsing response:', e);
                        showError(contentId, 'Failed to load tests. Please try again.');
                    }
                } else {
                    console.error('Error loading tests:', xhr.status);
                    showError(contentId, 'Failed to load tests. Please try again.');
                }
            }
        };

        xhr.send();
    }

    // Function to render tests
    function renderTests(tests, contentId) {
        var contentElement = document.getElementById(contentId);

        if (!tests || tests.length === 0) {
            contentElement.innerHTML = '<div class="no-tests">' +
                '<i class="fa fa-inbox fa-3x" style="color: #dee2e6; margin-bottom: 15px;"></i>' +
                '<h4>No tests found</h4>' +
                '<p>There are no tests available in this category.</p>' +
                '</div>';
            return;
        }

        var html = '<div class="tests-grid">';
        for (var i = 0; i < tests.length; i++) {
            html += renderTestCard(tests[i]);
        }
        html += '</div>';

        contentElement.innerHTML = html;
    }

    // Function to render individual test card
    function renderTestCard(test) {
        // Format dates
        var startDate = test.testStartDate ? formatDate(new Date(test.testStartDate)) : 'Not set';
        var endDate = test.testEndDate ? formatDate(new Date(test.testEndDate)) : 'Not set';

        // Language flags
        var languageFlags = '';
        if (test.language1) {
            languageFlags += '<div class="language-flag">' +
                '<i class="fa-solid fa-language"></i>' + test.language1 +
                '</div>';
        }
        if (test.language2) {
            languageFlags += '<div class="language-flag">' +
                '<i class="fa-solid fa-language"></i>' + test.language2 +
                '</div>';
        }

        // Action button based on status and access
        var actionButton = '';
        var buttonClass = '';
        var buttonText = '';
        var buttonIcon = '';

        if (test.hasTestAccess) {
            if (test.status === 'ongoing') {
                buttonClass = 'btn-start-now';
                buttonText = 'Start Now';
                buttonIcon = '';
                actionButton = '<a href="#" class="action-btn ' + buttonClass + '" onclick="startTest(' + test.mcqResId + ')">' + buttonText + '</a>';
            } else if (test.status === 'upcoming') {
                buttonClass = 'btn-disabled';
                buttonText = 'Starts Soon';
                actionButton = '<span class="action-btn ' + buttonClass + '" title="Test will start on ' + startDate + '">' + buttonText + '</span>';
            } else if (test.status === 'completed') {
                buttonClass = 'btn-view-results';
                buttonText = 'View Results';
                actionButton = '<a href="#" class="action-btn ' + buttonClass + '" onclick="viewResults(' + test.mcqResId + ')">' + buttonText + '</a>';
            } else {
                buttonClass = 'btn-register';
                buttonText = 'Register';
                actionButton = '<a href="#" class="action-btn ' + buttonClass + '" onclick="registerTest(' + test.mcqResId + ')">' + buttonText + '</a>';
            }
        } else {
            buttonClass = 'btn-disabled';
            buttonText = 'No Access';
            actionButton = '<span class="action-btn ' + buttonClass + '">' + buttonText + '</span>';
        }

        return '<div class="test-card">' +
            '<div class="test-card-content">' +
                '<div class="test-title">' + (test.resourceName || 'Untitled Test') + '</div>' +
                '<div class="test-info">' +
                    '<div class="info-item">' +
                        '<i class="fa-solid fa-file-text"></i>' +
                        '<span>' + (test.mcqCount || 0) + ' Questions</span>' +
                    '</div>' +
                    '<div class="info-item">' +
                        '<i class="fa-solid fa-clock"></i>' +
                        '<span>' + (test.totalTime || 0) + ' Mins.</span>' +
                    '</div>' +
                '</div>' +
                '<div class="test-datetime">' +
                    '<i class="fa-solid fa-clock"></i>' +
                    '<span>' + startDate + ' to ' + endDate + '</span>' +
                '</div>' +
                '<div class="language-flags">' + languageFlags + '</div>' +
                '<div class="test-action">' + actionButton + '</div>' +
            '</div>' +
        '</div>';
    }

    // Function to render pagination
    function renderPagination(pagination, paginationId, tabType) {
        var paginationElement = document.getElementById(paginationId);

        if (!pagination || pagination.totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }

        var currentPageNum = pagination.currentPage;
        var totalPages = pagination.totalPages;

        var paginationHtml = '<ul class="pagination">';

        // Page info
        paginationHtml += '<li class="disabled"><span>Page ' + currentPageNum + ' of ' + totalPages + '</span></li>';

        // Previous button
        if (currentPageNum > 1) {
            paginationHtml += '<li><a href="#" onclick="changePage(' + (currentPageNum - 1) + ', \'' + tabType + '\')">&laquo;</a></li>';
        } else {
            paginationHtml += '<li class="disabled"><span>&laquo;</span></li>';
        }

        // Page numbers
        var startPage = Math.max(1, currentPageNum - 2);
        var endPage = Math.min(totalPages, currentPageNum + 2);

        for (var i = startPage; i <= endPage; i++) {
            if (i === currentPageNum) {
                paginationHtml += '<li class="active"><span>' + i + '</span></li>';
            } else {
                paginationHtml += '<li><a href="#" onclick="changePage(' + i + ', \'' + tabType + '\')">' + i + '</a></li>';
            }
        }

        // Next button
        if (currentPageNum < totalPages) {
            paginationHtml += '<li><a href="#" onclick="changePage(' + (currentPageNum + 1) + ', \'' + tabType + '\')">&raquo;</a></li>';
        } else {
            paginationHtml += '<li class="disabled"><span>&raquo;</span></li>';
        }

        paginationHtml += '</ul>';
        paginationElement.innerHTML = paginationHtml;
    }

    // Handle pagination clicks
    window.changePage = function(page, tabType) {
        if (page && tabType) {
            loadTests(tabType, page);
        }
    };

    // Function to show error message
    function showError(contentId, message) {
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="no-tests">' +
            '<i class="fa fa-exclamation-triangle fa-3x" style="color: #dc3545; margin-bottom: 15px;"></i>' +
            '<h4>Error</h4>' +
            '<p>' + message + '</p>' +
            '<button class="btn btn-primary" onclick="loadTests(\'' + currentTab + '\', ' + currentPage[currentTab] + ')">' +
                '<i class="fa fa-refresh"></i> Retry' +
            '</button>' +
            '</div>';
    }

    // Utility function to format date
    function formatDate(date) {
        if (!date) return 'Not set';
        var options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return date.toLocaleDateString('en-US', options);
    }

    // Function to start a test
    window.startTest = function(mcqResId) {
        console.log('Starting test with mcqResId:', mcqResId);
        alert('Test start functionality to be implemented. MCQ Resource ID: ' + mcqResId);
    };

    // Function to register for a test
    window.registerTest = function(mcqResId) {
        console.log('Registering for test with mcqResId:', mcqResId);
        alert('Test registration functionality to be implemented. MCQ Resource ID: ' + mcqResId);
    };

    // Function to view test results
    window.viewResults = function(mcqResId) {
        console.log('Viewing results for mcqResId:', mcqResId);
        alert('Results view functionality to be implemented. MCQ Resource ID: ' + mcqResId);
    };
});
</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>