<%@ page contentType="text/html;charset=UTF-8" %>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<html>
<head>
    <title>Question Papers - ${bookTitle}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .qp-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 160px);
        }
        
        .qp-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #333;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .qp-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .qp-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .qp-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: #007bff !important;
            color: white !important;
            border: 1px solid #007bff;
        }

        .btn-primary:hover {
            background: #0056b3 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-success {
            background: #28a745 !important;
            color: white !important;
            border: 1px solid #28a745;
        }

        .btn-success:hover {
            background: #1e7e34 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-secondary {
            background: #6c757d !important;
            color: white !important;
            border: 1px solid #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
        }
        
        .qp-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        
        .qp-list-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .qp-list-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .qp-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .qp-table th {
            background: #f8f9fa;
            padding: 15px 20px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e9ecef;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .qp-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
            color: #555;
            vertical-align: middle;
        }
        
        .qp-table tr:hover {
            background: #f8f9fa;
        }
        
        .qp-name {
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }
        
        .qp-header-text {
            color: #666;
            font-size: 0.9rem;
            margin-top: 3px;
        }
        
        .qp-date {
            color: #666;
            font-size: 0.9rem;
        }
        
        .qp-marks {
            font-weight: 600;
            color: #28a745;
            font-size: 1rem;
        }
        
        .qp-actions-cell {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #333;
        }
        
        .empty-state p {
            font-size: 1.1rem;
            margin-bottom: 30px;
        }
        
        @media (max-width: 768px) {
            .qp-container {
                padding: 15px;
            }
            
            .qp-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .qp-table {
                font-size: 0.9rem;
            }
            
            .qp-table th,
            .qp-table td {
                padding: 10px 15px;
            }
            
            .qp-actions-cell {
                flex-direction: column;
                gap: 5px;
            }
            
            .btn-sm {
                width: 100%;
                justify-content: center;
            }
        }
        
        @media (max-width: 600px) {
            .qp-table,
            .qp-table thead,
            .qp-table tbody,
            .qp-table th,
            .qp-table td,
            .qp-table tr {
                display: block;
            }
            
            .qp-table thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }
            
            .qp-table tr {
                border: 1px solid #ccc;
                margin-bottom: 10px;
                padding: 15px;
                border-radius: 8px;
                background: white;
            }
            
            .qp-table td {
                border: none;
                position: relative;
                padding: 8px 0 8px 30%;
                text-align: left;
            }
            
            .qp-table td:before {
                content: attr(data-label) ": ";
                position: absolute;
                left: 0;
                width: 25%;
                padding-right: 10px;
                white-space: nowrap;
                font-weight: 600;
                color: #333;
            }
        }
    </style>
</head>
<body>
    <div class="qp-container">
        <!-- Header -->
        <div class="qp-header">
            <h1><i class="fas fa-list-alt"></i> Question Papers</h1>
            <p>Manage question papers for ${bookTitle}</p>
        </div>

        <!-- Actions -->
        <div class="qp-actions">
            <a href="/wpmain/qpcreator?bookId=${bookId}" class="btn btn-success">
                <i class="fas fa-plus"></i> Create New Question Paper
            </a>
            <a href="/wpmain/aibook?bookId=${bookId}" class="btn btn-secondary">
                <i class="fas fa-book"></i> Back to Book
            </a>
        </div>

        <!-- Question Papers List -->
        <div class="qp-list">
            <div class="qp-list-header">
                <h2 class="qp-list-title">
                    <i class="fas fa-file-alt"></i>
                    All Question Papers
                    <g:if test="${questionPapers}">
                        <span style="font-size: 0.8em; color: #666; font-weight: normal;">(${questionPapers.size()} total)</span>
                    </g:if>
                </h2>
            </div>

            <g:if test="${questionPapers && questionPapers.size() > 0}">
                <table class="qp-table">
                    <thead>
                        <tr>
                            <th>Question Paper Name</th>
                            <th>Date Created</th>
                            <th>Total Marks</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <g:each in="${questionPapers}" var="qp">
                            <tr>
                                <td data-label="Name">
                                    <div class="qp-name">${qp.patternName}</div>
                                    <g:if test="${qp.header}">
                                        <div class="qp-header-text">${qp.header}</div>
                                    </g:if>
                                </td>
                                <td data-label="Date Created">
                                    <div class="qp-date">
                                        <g:formatDate date="${qp.dateCreated}" format="dd-MMMM-yyyy" locale="en_IN"/>
                                    </div>
                                </td>
                                <td data-label="Total Marks">
                                    <div class="qp-marks">${qp.totalMarks ?: 'N/A'}</div>
                                </td>
                                <td data-label="Actions">
                                    <div class="qp-actions-cell">
                                        <a href="/wpmain/qpview?id=${qp.id}&bookId=${bookId}" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="/wpmain/qpprint?id=${qp.id}" 
                                           target="_blank" 
                                           class="btn btn-secondary btn-sm">
                                            <i class="fas fa-print"></i> Print
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </g:each>
                    </tbody>
                </table>
            </g:if>
            <g:else>
                <div class="empty-state">
                    <i class="fas fa-file-alt"></i>
                    <h3>No Question Papers Found</h3>
                    <p>You haven't created any question papers for this book yet.</p>
                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                        <a href="/wpmain/qpcreator?bookId=${bookId}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Create Question Paper
                        </a>
                        <a href="/wpmain/mockpapercreator?bookId=${bookId}" class="btn btn-primary">
                            <i class="fas fa-magic"></i> Create Mock Paper (Quick)
                        </a>
                    </div>
                </div>
            </g:else>
        </div>
    </div>
</body>
</html>

<g:render template="/${session['entryController']}/footer_new"></g:render>
