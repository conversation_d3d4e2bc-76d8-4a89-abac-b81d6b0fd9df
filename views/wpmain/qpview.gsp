<%@ page contentType="text/html;charset=UTF-8" %>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<html>
<head>
    <title>Question Paper - ${questionPaper.patternName}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" crossorigin="anonymous"></script>
    <style>
        .qp-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 160px);
        }
        
        .qp-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #333;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .qp-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .qp-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .qp-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: #007bff !important;
            color: white !important;
            border: 1px solid #007bff;
        }

        .btn-primary:hover {
            background: #0056b3 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-success {
            background: #28a745 !important;
            color: white !important;
            border: 1px solid #28a745;
        }

        .btn-success:hover {
            background: #1e7e34 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-secondary {
            background: #6c757d !important;
            color: white !important;
            border: 1px solid #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .question-paper {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }
        
        .paper-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .paper-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }
        
        .paper-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 15px;
        }
        
        .paper-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin: 0 0 5px 0;
        }
        
        .section-info {
            font-size: 0.9rem;
            color: #666;
            margin: 0;
        }
        
        .question {
            margin-bottom: 25px;
            padding: 20px;
            background: #fafafa;
            border-radius: 8px;
            border-left: 3px solid #667eea;
        }
        
        .question-number {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .question-text {
            font-size: 1rem;
            line-height: 1.6;
            color: #333;
            margin-bottom: 15px;
        }
        
        .question-options {
            margin-left: 20px;
        }
        
        .option {
            margin-bottom: 8px;
            font-size: 0.95rem;
            color: #555;
        }

        .match-following-header {
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .match-following-container {
            display: flex;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .match-column {
            flex: 1;
            min-width: 200px;
        }

        .match-column h4 {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
        }

        .match-item {
            margin-bottom: 8px;
            font-size: 0.95rem;
            color: #555;
            padding: 5px 0;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .qp-container {
                padding: 15px;
            }
            
            .question-paper {
                padding: 20px;
            }
            
            .paper-info {
                flex-direction: column;
                text-align: center;
            }
            
            .qp-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="qp-container">
        <!-- Header -->
        <div class="qp-header">
            <h1><i class="fas fa-file-alt"></i> ${questionPaper.patternName}</h1>
            <p>${questionPaper.header}</p>
        </div>

        <!-- Actions -->
        <div class="qp-actions">
            <a href="/wpmain/qpprint?id=${questionPaper.id}" target="_blank" class="btn btn-primary">
                <i class="fas fa-print"></i> Print
            </a>
            <button type="button" class="btn btn-success" onclick="openGenerateNewModal()">
                <i class="fas fa-plus"></i> Generate New
            </button>
            <g:if test="${params.fromMock == 'true' || params.fromMock == true}">
                <a href="/wpmain/mockpaperlist?bookId=${bookId}" class="btn btn-secondary">
                    <i class="fas fa-list"></i> Back to Mock Papers
                </a>
            </g:if>
            <g:else>
                <a href="/wpmain/qplist?bookId=${bookId}" class="btn btn-secondary">
                    <i class="fas fa-list"></i> Back to List
                </a>
            </g:else>
        </div>

        <!-- Question Paper Content -->
        <div class="question-paper">
            <div class="paper-header">
                <div class="paper-title">${questionPaper.patternName}</div>
                <div class="paper-subtitle">${questionPaper.header}</div>
                <div class="paper-info">
                    <span>Total Marks: ${questionPaper.totalMarks}</span>
                    <span>Date: <g:formatDate date="${questionPaper.dateCreated}" format="dd-MM-yyyy"/></span>
                    <span>Created by: ${questionPaper.createdBy}</span>
                </div>
            </div>

            <g:each in="${sections}" var="section" status="sectionIndex">
                <div class="section">
                    <div class="section-header">
                        <div class="section-title">${section.sectionHeading}</div>
                        <div class="section-info">
                            ${section.noOfQuestions} questions × ${section.totalMarks / section.noOfQuestions} marks each = ${section.totalMarks} marks
                        </div>
                    </div>

                    <g:each in="${section.questions}" var="question" status="questionIndex">
                        <div class="question">
                            <div class="question-number">
                                Q${sectionIndex + 1}.${questionIndex + 1}
                            </div>
                            <div class="question-text">
                                <g:if test="${question.qType == 'MatchFollowing'}">
                                    <script>
                                        // Parse and format MatchFollowing question
                                        document.addEventListener("DOMContentLoaded", function() {
                                            formatMatchFollowingQuestion("${question.id}");
                                        });
                                    </script>
                                    <div id="match-question-${question.id}" data-question="${question.question?.encodeAsHTML()}">
                                        ${question.question}
                                    </div>
                                </g:if>
                                <g:else>
                                    ${question.question}
                                </g:else>
                            </div>
                            <g:if test="${question.qType == 'MCQ'}">
                                <div class="question-options">
                                    <g:if test="${question.option1}">
                                        <div class="option">(a) ${question.option1}</div>
                                    </g:if>
                                    <g:if test="${question.option2}">
                                        <div class="option">(b) ${question.option2}</div>
                                    </g:if>
                                    <g:if test="${question.option3}">
                                        <div class="option">(c) ${question.option3}</div>
                                    </g:if>
                                    <g:if test="${question.option4}">
                                        <div class="option">(d) ${question.option4}</div>
                                    </g:if>
                                    <g:if test="${question.option5}">
                                        <div class="option">(e) ${question.option5}</div>
                                    </g:if>
                                </div>
                            </g:if>
                        </div>
                    </g:each>
                </div>
            </g:each>
        </div>
    </div>

    <!-- Generate New Modal -->
    <div id="generateNewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Generate New Question Paper</h3>
            </div>
            <div class="form-group">
                <label class="form-label" for="newPaperName">New Question Paper Name</label>
                <input type="text" id="newPaperName" class="form-control" placeholder="Enter new paper name">
            </div>
            <div class="loading-spinner" id="generatingNewPaper">
                <div class="spinner"></div>
                <p>Generating new question paper...</p>
            </div>
            <div class="modal-actions" id="modalActions">
                <button type="button" class="btn btn-secondary" onclick="closeGenerateNewModal()">Cancel</button>
                <button type="button" class="btn btn-success" onclick="generateNewPaper()">Generate</button>
            </div>
        </div>
    </div>

    <script>
        // Render math formulas
        document.addEventListener("DOMContentLoaded", function() {
            if (typeof renderMathInElement !== "undefined") {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\(", right: "\\)", display: false},
                        {left: "\\[", right: "\\]", display: true}
                    ]
                });
            }
        });

        function formatMatchFollowingQuestion(questionId) {
            var questionElement = document.getElementById("match-question-" + questionId);
            if (!questionElement) return;

            var questionText = questionElement.getAttribute("data-question");
            if (!questionText || !questionText.includes("Match the following")) return;

            // Parse the question text
            var parts = questionText.split(/Match the following[:\s]*/i);
            if (parts.length < 2) return;

            var header = "Match the following:";
            var content = parts[1].trim();

            // Normalize line breaks first
            content = content.replace(/<br\s*\/?>/gi, '\n');

            var leftColumn = [];
            var rightColumn = [];

            // Try to identify if there are explicit "Column A" and "Column B" labels
            var hasExplicitColumns = content.match(/Column\s+[AB]/i);

            if (hasExplicitColumns) {
                // Parse content with explicit column labels
                var columnAMatch = content.match(/Column\s+A[:\s]*(.*?)(?=Column\s+B|$)/is);
                var columnBMatch = content.match(/Column\s+B[:\s]*(.*?)$/is);

                if (columnAMatch && columnBMatch) {
                    leftColumn = parseColumnItems(columnAMatch[1]);
                    rightColumn = parseColumnItems(columnBMatch[1]);
                }
            } else {
                // Try different splitting strategies
                var sections = [];

                // Strategy 1: Split by double line breaks
                sections = content.split(/\n\s*\n/);

                if (sections.length >= 2) {
                    // Parse each section
                    leftColumn = parseColumnItems(sections[0]);
                    rightColumn = parseColumnItems(sections[1]);
                } else {
                    // Strategy 2: Extract all items and split by pattern change
                    var allItems = extractAllListItems(content);

                    if (allItems.length >= 2) {
                        var firstColumnItems = [];
                        var secondColumnItems = [];
                        var switchedToSecond = false;

                        for (var i = 0; i < allItems.length; i++) {
                            var item = allItems[i];

                            if (!switchedToSecond && firstColumnItems.length > 0) {
                                // Check if this item type is different from the first column
                                var firstItemType = getItemType(firstColumnItems[0]);
                                var currentItemType = getItemType(item);
                                if (firstItemType !== currentItemType) {
                                    switchedToSecond = true;
                                }
                            }

                            if (switchedToSecond) {
                                secondColumnItems.push(item);
                            } else {
                                firstColumnItems.push(item);
                            }
                        }

                        leftColumn = firstColumnItems;
                        rightColumn = secondColumnItems;
                    }
                }
            }

            // If we still don't have both columns, try a more aggressive approach
            if (leftColumn.length === 0 || rightColumn.length === 0) {
                var allItems = extractAllListItems(content);
                if (allItems.length >= 2) {
                    var midPoint = Math.ceil(allItems.length / 2);
                    leftColumn = allItems.slice(0, midPoint);
                    rightColumn = allItems.slice(midPoint);
                }
            }

            // Create the formatted HTML
            var formattedHTML = "<div class=\"match-following-header\">" + header + "</div>" +
                               "<div class=\"match-following-container\">" +
                               "<div class=\"match-column\">" +
                               "<h4>Column A</h4>";

            leftColumn.forEach(function(item) {
                formattedHTML += "<div class=\"match-item\">" + item + "</div>";
            });

            formattedHTML += "</div><div class=\"match-column\">" +
                            "<h4>Column B</h4>";

            rightColumn.forEach(function(item) {
                formattedHTML += "<div class=\"match-item\">" + item + "</div>";
            });

            formattedHTML += "</div></div>";

            questionElement.innerHTML = formattedHTML;
        }

        function parseColumnItems(text) {
            if (!text) return [];

            var items = [];

            // First try to split by line breaks
            var lines = text.split(/\n/).filter(function(line) {
                return line.trim().length > 0;
            });

            if (lines.length > 1) {
                // Multiple lines - parse each line
                lines.forEach(function(line) {
                    line = line.trim();
                    if (line.match(/^(\d+\.|[A-Za-z]\.|[A-Za-z]\)|\(\w+\))/)) {
                        items.push(line);
                    }
                });
            } else {
                // Single line - extract items using regex
                var singleLine = text.trim();
                items = extractItemsFromSingleLine(singleLine);
            }

            return items;
        }

        function extractItemsFromSingleLine(text) {
            var items = [];

            // Try multiple regex patterns to extract items
            var patterns = [
                // Pattern 1: "1. text" followed by space and next item
                /(\d+\.\s*[^A-Z\d\(]+?)(?=\s*(?:\d+\.|[A-Z]\.|[a-z]\.|[A-Z]\s|\([a-zA-Z0-9]\)|$))/g,
                // Pattern 2: "A. text" followed by space and next item
                /([A-Z]\.\s*[^A-Z\d\(]+?)(?=\s*(?:\d+\.|[A-Z]\.|[a-z]\.|[A-Z]\s|\([a-zA-Z0-9]\)|$))/g,
                // Pattern 3: "(a) text" followed by space and next item
                /(\([a-zA-Z0-9]\)\s*[^A-Z\d\(]+?)(?=\s*(?:\d+\.|[A-Z]\.|[a-z]\.|[A-Z]\s|\([a-zA-Z0-9]\)|$))/g
            ];

            // Try each pattern
            patterns.forEach(function(pattern) {
                var matches = text.match(pattern);
                if (matches) {
                    matches.forEach(function(match) {
                        var cleaned = match.trim();
                        if (cleaned && items.indexOf(cleaned) === -1) {
                            items.push(cleaned);
                        }
                    });
                }
            });

            // If no patterns worked, try a simpler approach
            if (items.length === 0) {
                // Split by common separators and filter for list items
                var parts = text.split(/\s+(?=\d+\.|[A-Z]\.|[a-z]\.|\([a-zA-Z0-9]\))/);
                parts.forEach(function(part) {
                    part = part.trim();
                    if (part && part.match(/^(\d+\.|[A-Za-z]\.|[A-Za-z]\)|\([a-zA-Z0-9]\))/)) {
                        items.push(part);
                    }
                });
            }

            return items;
        }

        function getItemType(item) {
            if (/^\d+\./.test(item)) return 'numbered';
            if (/^[A-Z]\./.test(item)) return 'uppercase';
            if (/^[a-z]\./.test(item)) return 'lowercase';
            if (/^\([a-zA-Z0-9]\)/.test(item)) return 'parenthetical';
            return 'other';
        }

        function extractAllListItems(text) {
            var items = [];

            // First try line-by-line approach
            var lines = text.split(/\n/);
            var foundLineItems = false;

            lines.forEach(function(line) {
                line = line.trim();
                if (line && line.match(/^(\d+\.|[A-Za-z]\.|[A-Za-z]\)|\(\w+\))/)) {
                    items.push(line);
                    foundLineItems = true;
                }
            });

            // If no line items found, try single-line extraction
            if (!foundLineItems) {
                items = extractItemsFromSingleLine(text);
            }

            return items;
        }

        function openGenerateNewModal() {
            document.getElementById("generateNewModal").style.display = "block";
            document.getElementById("newPaperName").value = "";
        }

        function closeGenerateNewModal() {
            document.getElementById("generateNewModal").style.display = "none";
        }

        function generateNewPaper() {
            var newPaperName = document.getElementById("newPaperName").value.trim();
            if (!newPaperName) {
                alert("Please enter a name for the new question paper");
                return;
            }

            document.getElementById("modalActions").style.display = "none";
            document.getElementById("generatingNewPaper").style.display = "block";

            var formData = new FormData();
            formData.append("originalPaperId", "${questionPaper.id}");
            formData.append("newPaperName", newPaperName);

            fetch("/wpmain/generateNewQuestionPaper", {
                method: "POST",
                body: formData
            })
            .then(function(response) { return response.json(); })
            .then(function(data) {
                if (data.success) {
                    window.location.href = "/wpmain/qpview?id=" + data.questionPaperId + "&bookId=${bookId}";
                } else {
                    alert("Error generating new question paper: " + data.message);
                    document.getElementById("generatingNewPaper").style.display = "none";
                    document.getElementById("modalActions").style.display = "flex";
                }
            })
            .catch(function(error) {
                console.error("Error:", error);
                alert("Error generating new question paper");
                document.getElementById("generatingNewPaper").style.display = "none";
                document.getElementById("modalActions").style.display = "flex";
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            var modal = document.getElementById("generateNewModal");
            if (event.target == modal) {
                closeGenerateNewModal();
            }
        }
    </script>
</body>
</html>

<g:render template="/${session['entryController']}/footer_new"></g:render>
